import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import {RecommendationsService} from '../services/RecommendationsService';
import {GamificationService} from '../services/GamificationService';

interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: string;
  impact: 'High' | 'Medium' | 'Low';
  effort: 'Easy' | 'Medium' | 'Hard';
  carbonSaving: number;
  icon: string;
  completed: boolean;
}

const RecommendationsScreen: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecommendations();
  }, []);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      const recs = await RecommendationsService.getPersonalizedRecommendations();
      setRecommendations(recs);
    } catch (error) {
      console.error('Error loading recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleRecommendation = async (id: string) => {
    try {
      const recommendation = recommendations.find(rec => rec.id === id);
      if (recommendation && !recommendation.completed) {
        // Complete the recommendation
        await RecommendationsService.toggleRecommendation(id);
        await GamificationService.completeRecommendation(recommendation.carbonSaving);
      } else {
        // Uncomplete the recommendation
        await RecommendationsService.toggleRecommendation(id);
      }

      // Reload recommendations to reflect changes
      await loadRecommendations();
    } catch (error) {
      console.error('Error toggling recommendation:', error);
    }
  };

  const filters = ['All', 'Transport', 'Energy', 'Food', 'Shopping'];

  const filteredRecommendations = recommendations.filter(
    rec => selectedFilter === 'All' || rec.category === selectedFilter,
  );

  const getImpactColor = (impact: string): string => {
    switch (impact) {
      case 'High':
        return '#FF5722';
      case 'Medium':
        return '#FF9800';
      case 'Low':
        return '#4CAF50';
      default:
        return '#666';
    }
  };

  const getEffortColor = (effort: string): string => {
    switch (effort) {
      case 'Easy':
        return '#4CAF50';
      case 'Medium':
        return '#FF9800';
      case 'Hard':
        return '#FF5722';
      default:
        return '#666';
    }
  };

  const totalPotentialSaving = recommendations
    .filter(rec => !rec.completed)
    .reduce((sum, rec) => sum + rec.carbonSaving, 0);

  const completedCount = recommendations.filter(rec => rec.completed).length;

  const renderRecommendation = ({item}: {item: Recommendation}) => (
    <TouchableOpacity
      style={[
        styles.recommendationCard,
        item.completed && styles.completedCard,
      ]}
      onPress={() => toggleRecommendation(item.id)}>
      <View style={styles.cardHeader}>
        <View style={styles.iconContainer}>
          <Icon
            name={item.icon}
            size={24}
            color={item.completed ? '#666' : '#4CAF50'}
          />
        </View>
        <View style={styles.cardContent}>
          <Text
            style={[styles.cardTitle, item.completed && styles.completedText]}>
            {item.title}
          </Text>
          <Text
            style={[
              styles.cardDescription,
              item.completed && styles.completedText,
            ]}>
            {item.description}
          </Text>
        </View>
        <View style={styles.checkContainer}>
          <Icon
            name={item.completed ? 'check-circle' : 'radio-button-unchecked'}
            size={24}
            color={item.completed ? '#4CAF50' : '#ccc'}
          />
        </View>
      </View>

      <View style={styles.cardFooter}>
        <View style={styles.tagContainer}>
          <View
            style={[
              styles.tag,
              {backgroundColor: getImpactColor(item.impact) + '20'},
            ]}>
            <Text
              style={[styles.tagText, {color: getImpactColor(item.impact)}]}>
              {item.impact} Impact
            </Text>
          </View>
          <View
            style={[
              styles.tag,
              {backgroundColor: getEffortColor(item.effort) + '20'},
            ]}>
            <Text
              style={[styles.tagText, {color: getEffortColor(item.effort)}]}>
              {item.effort}
            </Text>
          </View>
        </View>
        <View style={styles.savingContainer}>
          <Text style={styles.savingValue}>-{item.carbonSaving} kg</Text>
          <Text style={styles.savingLabel}>CO₂/day</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient colors={['#4CAF50', '#45A049']} style={styles.header}>
        <Text style={styles.headerTitle}>Eco Recommendations</Text>
        <Text style={styles.headerSubtitle}>
          Potential saving: {totalPotentialSaving.toFixed(1)} kg CO₂/day
        </Text>
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            {completedCount}/{recommendations.length} completed
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {width: `${(completedCount / recommendations.length) * 100}%`},
              ]}
            />
          </View>
        </View>
      </LinearGradient>

      {/* Filter Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}>
        {filters.map(filter => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterTab,
              selectedFilter === filter && styles.activeFilterTab,
            ]}
            onPress={() => setSelectedFilter(filter)}>
            <Text
              style={[
                styles.filterText,
                selectedFilter === filter && styles.activeFilterText,
              ]}>
              {filter}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Recommendations List */}
      <FlatList
        data={filteredRecommendations}
        renderItem={renderRecommendation}
        keyExtractor={item => item.id}
        style={styles.recommendationsList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: '#fff',
    fontSize: 16,
    opacity: 0.9,
    marginTop: 4,
  },
  progressContainer: {
    marginTop: 16,
  },
  progressText: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 8,
  },
  progressBar: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 3,
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#fff',
  },
  activeFilterTab: {
    backgroundColor: '#4CAF50',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterText: {
    color: '#fff',
  },
  recommendationsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  recommendationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedCard: {
    opacity: 0.7,
    backgroundColor: '#f8f8f8',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  cardContent: {
    flex: 1,
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: '#999',
  },
  checkContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tagContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  savingContainer: {
    alignItems: 'flex-end',
  },
  savingValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  savingLabel: {
    fontSize: 12,
    color: '#666',
  },
});

export default RecommendationsScreen;
