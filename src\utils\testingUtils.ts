import {CarbonFootprintService} from '../services/CarbonFootprintService';
import {GamificationService} from '../services/GamificationService';
import {RecommendationsService} from '../services/RecommendationsService';
import {NotificationService} from '../services/NotificationService';
import {DataSeedingService} from '../services/DataSeedingService';
import {Activity} from '../types';

export class TestingUtils {
  // Test all core services
  static async runComprehensiveTest(): Promise<{
    success: boolean;
    results: Record<string, boolean>;
    errors: string[];
  }> {
    const results: Record<string, boolean> = {};
    const errors: string[] = [];

    console.log('🧪 Starting comprehensive app testing...');

    try {
      // Test CarbonFootprintService
      console.log('Testing CarbonFootprintService...');
      results.carbonFootprintService = await this.testCarbonFootprintService();
    } catch (error) {
      results.carbonFootprintService = false;
      errors.push(`CarbonFootprintService: ${error}`);
    }

    try {
      // Test GamificationService
      console.log('Testing GamificationService...');
      results.gamificationService = await this.testGamificationService();
    } catch (error) {
      results.gamificationService = false;
      errors.push(`GamificationService: ${error}`);
    }

    try {
      // Test RecommendationsService
      console.log('Testing RecommendationsService...');
      results.recommendationsService = await this.testRecommendationsService();
    } catch (error) {
      results.recommendationsService = false;
      errors.push(`RecommendationsService: ${error}`);
    }

    try {
      // Test NotificationService
      console.log('Testing NotificationService...');
      results.notificationService = await this.testNotificationService();
    } catch (error) {
      results.notificationService = false;
      errors.push(`NotificationService: ${error}`);
    }

    try {
      // Test DataSeedingService
      console.log('Testing DataSeedingService...');
      results.dataSeedingService = await this.testDataSeedingService();
    } catch (error) {
      results.dataSeedingService = false;
      errors.push(`DataSeedingService: ${error}`);
    }

    const allPassed = Object.values(results).every(result => result);

    console.log('🧪 Testing completed!');
    console.log('Results:', results);
    if (errors.length > 0) {
      console.log('Errors:', errors);
    }

    return {
      success: allPassed,
      results,
      errors,
    };
  }

  // Test CarbonFootprintService
  private static async testCarbonFootprintService(): Promise<boolean> {
    try {
      // Test initialization
      await CarbonFootprintService.initializeStorage();

      // Test saving an activity
      const testActivity: Activity = {
        id: 'test_activity_1',
        userId: 'test_user',
        category: 'Transport',
        description: 'Test commute',
        amount: 10,
        unit: 'km',
        carbonFootprint: 2.0,
        timestamp: new Date(),
      };

      await CarbonFootprintService.saveActivity(testActivity);

      // Test retrieving activities
      const activities = await CarbonFootprintService.getActivities();
      const hasTestActivity = activities.some(a => a.id === testActivity.id);

      // Test calculating daily footprint
      const dailyFootprint = await CarbonFootprintService.calculateDailyFootprint(new Date());

      // Test getting progress data
      const progressData = await CarbonFootprintService.getProgressData(7);

      // Test export functionality
      const exportData = await CarbonFootprintService.exportData();

      return hasTestActivity && 
             dailyFootprint.total >= 0 && 
             typeof progressData.improvement === 'number' &&
             exportData.length > 0;
    } catch (error) {
      console.error('CarbonFootprintService test failed:', error);
      return false;
    }
  }

  // Test GamificationService
  private static async testGamificationService(): Promise<boolean> {
    try {
      // Test initialization
      await GamificationService.initializeUserStats();

      // Test getting user stats
      const userStats = await GamificationService.getUserStats();

      // Test logging activity
      await GamificationService.logActivity();

      // Test getting achievements
      const achievements = await GamificationService.getAchievements();

      // Test completing recommendation
      await GamificationService.completeRecommendation(1.5);

      // Test getting updated stats
      const updatedStats = await GamificationService.getUserStats();

      return userStats !== null &&
             achievements.length > 0 &&
             updatedStats !== null &&
             updatedStats.totalPoints >= userStats.totalPoints;
    } catch (error) {
      console.error('GamificationService test failed:', error);
      return false;
    }
  }

  // Test RecommendationsService
  private static async testRecommendationsService(): Promise<boolean> {
    try {
      // Test initialization
      await RecommendationsService.initializeRecommendations();

      // Test getting recommendations
      const recommendations = await RecommendationsService.getRecommendations();

      // Test toggling a recommendation
      if (recommendations.length > 0) {
        const firstRec = recommendations[0];
        await RecommendationsService.toggleRecommendation(firstRec.id);
        
        const updatedRecommendations = await RecommendationsService.getRecommendations();
        const toggledRec = updatedRecommendations.find(r => r.id === firstRec.id);
        
        return recommendations.length > 0 && 
               toggledRec !== undefined &&
               toggledRec.completed !== firstRec.completed;
      }

      return recommendations.length > 0;
    } catch (error) {
      console.error('RecommendationsService test failed:', error);
      return false;
    }
  }

  // Test NotificationService
  private static async testNotificationService(): Promise<boolean> {
    try {
      // Test initialization
      await NotificationService.initializeSettings();

      // Test getting settings
      const settings = await NotificationService.getSettings();

      // Test updating a setting
      await NotificationService.updateSetting('dailyReminders', false);
      const updatedSettings = await NotificationService.getSettings();

      // Test resetting settings
      await NotificationService.resetSettings();
      const resetSettings = await NotificationService.getSettings();

      return settings !== null &&
             updatedSettings.dailyReminders === false &&
             resetSettings.dailyReminders === true; // Should be back to default
    } catch (error) {
      console.error('NotificationService test failed:', error);
      return false;
    }
  }

  // Test DataSeedingService
  private static async testDataSeedingService(): Promise<boolean> {
    try {
      // Clear existing seed data
      await DataSeedingService.clearSeedData();

      // Test seeding
      await DataSeedingService.seedSampleData();

      // Verify data was seeded
      const activities = await CarbonFootprintService.getActivities();
      const userStats = await GamificationService.getUserStats();

      return activities.length > 0 && userStats !== null && userStats.totalPoints > 0;
    } catch (error) {
      console.error('DataSeedingService test failed:', error);
      return false;
    }
  }

  // Generate test report
  static generateTestReport(testResults: {
    success: boolean;
    results: Record<string, boolean>;
    errors: string[];
  }): string {
    const { success, results, errors } = testResults;
    
    let report = '📊 EcoAI App Test Report\n';
    report += '========================\n\n';
    
    report += `Overall Status: ${success ? '✅ PASSED' : '❌ FAILED'}\n\n`;
    
    report += 'Service Tests:\n';
    Object.entries(results).forEach(([service, passed]) => {
      report += `  ${passed ? '✅' : '❌'} ${service}\n`;
    });
    
    if (errors.length > 0) {
      report += '\nErrors:\n';
      errors.forEach(error => {
        report += `  ❌ ${error}\n`;
      });
    }
    
    report += '\nTest Coverage:\n';
    report += '  ✅ Data persistence\n';
    report += '  ✅ Service initialization\n';
    report += '  ✅ CRUD operations\n';
    report += '  ✅ Gamification logic\n';
    report += '  ✅ Recommendation system\n';
    report += '  ✅ Notification settings\n';
    report += '  ✅ Data seeding\n';
    
    return report;
  }

  // Quick health check
  static async quickHealthCheck(): Promise<boolean> {
    try {
      const [activities, userStats, recommendations, notificationSettings] = await Promise.all([
        CarbonFootprintService.getActivities(),
        GamificationService.getUserStats(),
        RecommendationsService.getRecommendations(),
        NotificationService.getSettings(),
      ]);

      return activities !== null &&
             userStats !== null &&
             recommendations !== null &&
             notificationSettings !== null;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  // Performance test
  static async performanceTest(): Promise<{
    averageResponseTime: number;
    operations: Record<string, number>;
  }> {
    const operations: Record<string, number> = {};
    const startTime = Date.now();

    // Test various operations
    const testOperations = [
      { name: 'getActivities', fn: () => CarbonFootprintService.getActivities() },
      { name: 'getUserStats', fn: () => GamificationService.getUserStats() },
      { name: 'getRecommendations', fn: () => RecommendationsService.getRecommendations() },
      { name: 'getNotificationSettings', fn: () => NotificationService.getSettings() },
    ];

    for (const operation of testOperations) {
      const opStartTime = Date.now();
      await operation.fn();
      operations[operation.name] = Date.now() - opStartTime;
    }

    const totalTime = Date.now() - startTime;
    const averageResponseTime = totalTime / testOperations.length;

    return {
      averageResponseTime,
      operations,
    };
  }
}
