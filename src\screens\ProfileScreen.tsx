import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Share,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'expo-linear-gradient';
import {GamificationService} from '../services/GamificationService';
import {CarbonFootprintService} from '../services/CarbonFootprintService';
import {NotificationService, NotificationSettings} from '../services/NotificationService';
import {useFocusEffect} from '@react-navigation/native';
import {UserStats} from '../types';

interface UserProfile {
  name: string;
  email: string;
  location: string;
  householdSize: number;
  primaryTransport: string;
  dietType: string;
  energyProvider: string;
}

const ProfileScreen: React.FC = () => {
  const [profile] = useState<UserProfile>({
    name: '<PERSON>',
    email: '<EMAIL>',
    location: 'San Francisco, CA',
    householdSize: 2,
    primaryTransport: 'Car',
    dietType: 'Omnivore',
    energyProvider: 'PG&E',
  });

  const [notifications, setNotifications] = useState({
    dailyReminders: true,
    weeklyReports: true,
    achievements: true,
    recommendations: true,
  });

  const [ecoStats, setEcoStats] = useState<UserStats>({
    totalPoints: 0,
    currentStreak: 0,
    longestStreak: 0,
    totalCarbonSaved: 0,
    activitiesLogged: 0,
    recommendationsCompleted: 0,
    lastActivityDate: null,
    level: 1,
    rank: 'Eco Newbie',
  });

  const [achievements, setAchievements] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const loadProfileData = async () => {
    try {
      setIsLoading(true);

      // Load user stats
      const stats = await GamificationService.getUserStats();
      if (stats) {
        setEcoStats(stats);
      }

      // Load achievements
      const [allAchievements, unlockedAchievements] = await Promise.all([
        GamificationService.getAchievements(),
        GamificationService.getUnlockedAchievements(),
      ]);

      const achievementsWithStatus = allAchievements.map(achievement => ({
        ...achievement,
        unlocked: unlockedAchievements.includes(achievement.id),
      }));

      setAchievements(achievementsWithStatus);
    } catch (error) {
      console.error('Error loading profile data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadProfileData();
    }, [])
  );

  const toggleNotification = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleEditProfile = () => {
    Alert.alert('Edit Profile', 'Profile editing feature coming soon!');
  };

  const handleExportData = async () => {
    try {
      const exportData = await CarbonFootprintService.exportData();

      // Share the data
      await Share.share({
        message: 'My EcoAI Carbon Footprint Data',
        title: 'EcoAI Data Export',
        url: `data:application/json;base64,${btoa(exportData)}`,
      });
    } catch (error) {
      console.error('Error exporting data:', error);
      Alert.alert('Error', 'Failed to export data. Please try again.');
    }
  };

  const handleResetData = () => {
    Alert.alert(
      'Reset All Data',
      'Are you sure you want to reset all your data? This will delete all activities, achievements, and progress. This action cannot be undone.',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await Promise.all([
                CarbonFootprintService.clearAllData(),
                GamificationService.resetProgress(),
              ]);
              await loadProfileData();
              Alert.alert('Success', 'All data has been reset.');
            } catch (error) {
              console.error('Error resetting data:', error);
              Alert.alert('Error', 'Failed to reset data. Please try again.');
            }
          },
        },
      ],
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Delete', style: 'destructive'},
      ],
    );
  };

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <LinearGradient
        colors={['#4CAF50', '#45A049']}
        style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <Icon name="person" size={48} color="#fff" />
          </View>
          <TouchableOpacity style={styles.editAvatarButton}>
            <Icon name="camera-alt" size={16} color="#4CAF50" />
          </TouchableOpacity>
        </View>
        <Text style={styles.profileName}>{profile.name}</Text>
        <Text style={styles.profileEmail}>{profile.email}</Text>
        <Text style={styles.profileRank}>{ecoStats.rank}</Text>
      </LinearGradient>

      {/* Eco Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Icon name="eco" size={24} color="#4CAF50" />
          <Text style={styles.statValue}>{ecoStats.totalPoints}</Text>
          <Text style={styles.statLabel}>Eco Points</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="local-fire-department" size={24} color="#FF5722" />
          <Text style={styles.statValue}>{ecoStats.currentStreak}</Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="trending-down" size={24} color="#2196F3" />
          <Text style={styles.statValue}>{ecoStats.totalCarbonSaved.toFixed(1)}</Text>
          <Text style={styles.statLabel}>kg CO₂ Saved</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="military-tech" size={24} color="#FF9800" />
          <Text style={styles.statValue}>{achievements.filter(a => a.unlocked).length}</Text>
          <Text style={styles.statLabel}>Badges</Text>
        </View>
      </View>

      {/* Profile Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          <TouchableOpacity onPress={handleEditProfile}>
            <Icon name="edit" size={20} color="#4CAF50" />
          </TouchableOpacity>
        </View>

        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Icon name="location-on" size={20} color="#666" />
            <Text style={styles.infoLabel}>Location</Text>
            <Text style={styles.infoValue}>{profile.location}</Text>
          </View>
          <View style={styles.infoRow}>
            <Icon name="home" size={20} color="#666" />
            <Text style={styles.infoLabel}>Household Size</Text>
            <Text style={styles.infoValue}>{profile.householdSize} people</Text>
          </View>
          <View style={styles.infoRow}>
            <Icon name="directions-car" size={20} color="#666" />
            <Text style={styles.infoLabel}>Primary Transport</Text>
            <Text style={styles.infoValue}>{profile.primaryTransport}</Text>
          </View>
          <View style={styles.infoRow}>
            <Icon name="restaurant" size={20} color="#666" />
            <Text style={styles.infoLabel}>Diet Type</Text>
            <Text style={styles.infoValue}>{profile.dietType}</Text>
          </View>
          <View style={styles.infoRow}>
            <Icon name="flash-on" size={20} color="#666" />
            <Text style={styles.infoLabel}>Energy Provider</Text>
            <Text style={styles.infoValue}>{profile.energyProvider}</Text>
          </View>
        </View>
      </View>

      {/* Notification Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        <View style={styles.settingsCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Icon name="notifications" size={20} color="#666" />
              <Text style={styles.settingLabel}>Daily Reminders</Text>
            </View>
            <Switch
              value={notifications.dailyReminders}
              onValueChange={() => toggleNotification('dailyReminders')}
              trackColor={{false: '#ccc', true: '#4CAF50'}}
              thumbColor="#fff"
            />
          </View>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Icon name="assessment" size={20} color="#666" />
              <Text style={styles.settingLabel}>Weekly Reports</Text>
            </View>
            <Switch
              value={notifications.weeklyReports}
              onValueChange={() => toggleNotification('weeklyReports')}
              trackColor={{false: '#ccc', true: '#4CAF50'}}
              thumbColor="#fff"
            />
          </View>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Icon name="emoji-events" size={20} color="#666" />
              <Text style={styles.settingLabel}>Achievements</Text>
            </View>
            <Switch
              value={notifications.achievements}
              onValueChange={() => toggleNotification('achievements')}
              trackColor={{false: '#ccc', true: '#4CAF50'}}
              thumbColor="#fff"
            />
          </View>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Icon name="lightbulb" size={20} color="#666" />
              <Text style={styles.settingLabel}>Recommendations</Text>
            </View>
            <Switch
              value={notifications.recommendations}
              onValueChange={() => toggleNotification('recommendations')}
              trackColor={{false: '#ccc', true: '#4CAF50'}}
              thumbColor="#fff"
            />
          </View>
        </View>
      </View>

      {/* Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data & Privacy</Text>
        <View style={styles.actionsCard}>
          <TouchableOpacity style={styles.actionRow} onPress={handleExportData}>
            <Icon name="download" size={20} color="#4CAF50" />
            <Text style={styles.actionLabel}>Export My Data</Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionRow}>
            <Icon name="privacy-tip" size={20} color="#2196F3" />
            <Text style={styles.actionLabel}>Privacy Policy</Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionRow}>
            <Icon name="help" size={20} color="#FF9800" />
            <Text style={styles.actionLabel}>Help & Support</Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionRow}
            onPress={handleResetData}>
            <Icon name="refresh" size={20} color="#FF9800" />
            <Text style={styles.actionLabel}>Reset All Data</Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionRow, styles.dangerAction]}
            onPress={handleDeleteAccount}>
            <Icon name="delete" size={20} color="#F44336" />
            <Text style={[styles.actionLabel, styles.dangerText]}>
              Delete Account
            </Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
        </View>
      </View>

      {/* App Version */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>EcoAI v1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  profileHeader: {
    alignItems: 'center',
    padding: 32,
    paddingTop: 48,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    color: '#fff',
    fontSize: 16,
    opacity: 0.9,
    marginBottom: 8,
  },
  profileRank: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginTop: -20,
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
    textAlign: 'center',
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  infoValue: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  settingsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  actionsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionLabel: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  dangerAction: {
    borderBottomWidth: 0,
  },
  dangerText: {
    color: '#F44336',
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#999',
  },
});

export default ProfileScreen;
