import AsyncStorage from '@react-native-async-storage/async-storage';
import {Alert} from 'react-native';

export interface NotificationSettings {
  dailyReminders: boolean;
  achievementNotifications: boolean;
  weeklyReports: boolean;
  ecoTips: boolean;
  reminderTime: string; // HH:MM format
}

export class NotificationService {
  private static readonly STORAGE_KEY = 'ecoai_notification_settings';

  private static defaultSettings: NotificationSettings = {
    dailyReminders: true,
    achievementNotifications: true,
    weeklyReports: true,
    ecoTips: true,
    reminderTime: '19:00', // 7 PM default
  };

  // Initialize notification settings
  static async initializeSettings(): Promise<void> {
    try {
      const existingSettings = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (!existingSettings) {
        await this.saveSettings(this.defaultSettings);
        console.log('Notification settings initialized with defaults');
      }
    } catch (error) {
      console.error('Error initializing notification settings:', error);
    }
  }

  // Get current notification settings
  static async getSettings(): Promise<NotificationSettings> {
    try {
      const settingsJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (settingsJson) {
        return JSON.parse(settingsJson);
      }
      return this.defaultSettings;
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return this.defaultSettings;
    }
  }

  // Save notification settings
  static async saveSettings(settings: NotificationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(settings));
      console.log('Notification settings saved');
    } catch (error) {
      console.error('Error saving notification settings:', error);
      throw error;
    }
  }

  // Update a specific setting
  static async updateSetting(
    key: keyof NotificationSettings,
    value: boolean | string
  ): Promise<void> {
    try {
      const currentSettings = await this.getSettings();
      const updatedSettings = {
        ...currentSettings,
        [key]: value,
      };
      await this.saveSettings(updatedSettings);
    } catch (error) {
      console.error('Error updating notification setting:', error);
      throw error;
    }
  }

  // Show achievement notification
  static async showAchievementNotification(
    title: string,
    description: string,
    points: number
  ): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (settings.achievementNotifications) {
        Alert.alert(
          '🏆 Achievement Unlocked!',
          `${title}\n\n${description}\n\n+${points} points earned!`,
          [
            {
              text: 'Awesome!',
              style: 'default',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error showing achievement notification:', error);
    }
  }

  // Show daily reminder notification
  static async showDailyReminder(): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (settings.dailyReminders) {
        Alert.alert(
          '🌱 Daily Eco Reminder',
          "Don't forget to log your activities today! Every small action counts towards a greener future.",
          [
            {
              text: 'Log Activity',
              style: 'default',
            },
            {
              text: 'Later',
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error showing daily reminder:', error);
    }
  }

  // Show eco tip notification
  static async showEcoTip(tip: string): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (settings.ecoTips) {
        Alert.alert(
          '💡 Eco Tip of the Day',
          tip,
          [
            {
              text: 'Thanks!',
              style: 'default',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error showing eco tip:', error);
    }
  }

  // Show weekly report notification
  static async showWeeklyReport(
    carbonSaved: number,
    activitiesLogged: number,
    pointsEarned: number
  ): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (settings.weeklyReports) {
        Alert.alert(
          '📊 Weekly Eco Report',
          `Great week! Here's your impact:\n\n` +
          `🌱 Carbon saved: ${carbonSaved.toFixed(1)} kg CO₂\n` +
          `📝 Activities logged: ${activitiesLogged}\n` +
          `⭐ Points earned: ${pointsEarned}\n\n` +
          `Keep up the amazing work!`,
          [
            {
              text: 'View Details',
              style: 'default',
            },
            {
              text: 'OK',
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error showing weekly report:', error);
    }
  }

  // Show streak milestone notification
  static async showStreakMilestone(streakDays: number): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (settings.achievementNotifications) {
        let message = '';
        let emoji = '🔥';

        if (streakDays === 7) {
          message = 'One week of consistent eco tracking!';
          emoji = '🌟';
        } else if (streakDays === 30) {
          message = 'One month of amazing eco dedication!';
          emoji = '🏆';
        } else if (streakDays === 100) {
          message = '100 days! You\'re an eco champion!';
          emoji = '👑';
        } else {
          message = `${streakDays} days of consistent tracking!`;
        }

        Alert.alert(
          `${emoji} Streak Milestone!`,
          message + '\n\nYour commitment to sustainability is inspiring!',
          [
            {
              text: 'Keep Going!',
              style: 'default',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error showing streak milestone:', error);
    }
  }

  // Schedule daily reminder (placeholder for future implementation)
  static async scheduleDailyReminder(): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (settings.dailyReminders) {
        // In a real app, this would use expo-notifications or react-native-push-notification
        // to schedule actual push notifications
        console.log(`Daily reminder scheduled for ${settings.reminderTime}`);
      }
    } catch (error) {
      console.error('Error scheduling daily reminder:', error);
    }
  }

  // Cancel all scheduled notifications
  static async cancelAllNotifications(): Promise<void> {
    try {
      // In a real app, this would cancel all scheduled notifications
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Error cancelling notifications:', error);
    }
  }

  // Reset notification settings to defaults
  static async resetSettings(): Promise<void> {
    try {
      await this.saveSettings(this.defaultSettings);
      console.log('Notification settings reset to defaults');
    } catch (error) {
      console.error('Error resetting notification settings:', error);
      throw error;
    }
  }
}
