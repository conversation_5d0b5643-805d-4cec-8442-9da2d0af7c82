// Carbon emission factors (kg CO2 per unit)
export const EMISSION_FACTORS = {
  // Transport (kg CO2 per km)
  car_gasoline: 0.21,
  car_diesel: 0.17,
  car_electric: 0.05,
  bus: 0.08,
  train: 0.04,
  bicycle: 0,
  walking: 0,
  motorcycle: 0.15,
  airplane_domestic: 0.25,
  airplane_international: 0.3,

  // Energy (kg CO2 per kWh)
  electricity_grid: 0.4,
  electricity_renewable: 0.02,
  natural_gas: 0.2,
  heating_oil: 0.27,

  // Food (kg CO2 per kg or serving)
  beef: 27,
  lamb: 24,
  pork: 12,
  chicken: 6,
  fish: 4,
  dairy: 3.2,
  eggs: 4.2,
  vegetables: 0.4,
  fruits: 0.3,
  grains: 1.1,
  legumes: 0.9,

  // Shopping (kg CO2 per item/kg)
  clothing_new: 8,
  electronics: 300,
  furniture: 50,
  books: 1.2,
  plastic_bag: 0.006,
  paper_bag: 0.07,
};

export interface TransportActivity {
  mode: keyof typeof EMISSION_FACTORS;
  distance: number; // km
}

export interface EnergyActivity {
  type: keyof typeof EMISSION_FACTORS;
  consumption: number; // kWh
}

export interface FoodActivity {
  type: keyof typeof EMISSION_FACTORS;
  quantity: number; // kg or servings
}

export interface ShoppingActivity {
  type: keyof typeof EMISSION_FACTORS;
  quantity: number; // items or kg
}

export class CarbonCalculator {
  static calculateTransport(activity: TransportActivity): number {
    const factor = EMISSION_FACTORS[activity.mode];
    return factor * activity.distance;
  }

  static calculateEnergy(activity: EnergyActivity): number {
    const factor = EMISSION_FACTORS[activity.type];
    return factor * activity.consumption;
  }

  static calculateFood(activity: FoodActivity): number {
    const factor = EMISSION_FACTORS[activity.type];
    return factor * activity.quantity;
  }

  static calculateShopping(activity: ShoppingActivity): number {
    const factor = EMISSION_FACTORS[activity.type];
    return factor * activity.quantity;
  }

  static calculateDailyFootprint(activities: {
    transport: TransportActivity[];
    energy: EnergyActivity[];
    food: FoodActivity[];
    shopping: ShoppingActivity[];
  }): {
    transport: number;
    energy: number;
    food: number;
    shopping: number;
    total: number;
  } {
    const transport = activities.transport.reduce(
      (sum, activity) => sum + this.calculateTransport(activity),
      0,
    );

    const energy = activities.energy.reduce(
      (sum, activity) => sum + this.calculateEnergy(activity),
      0,
    );

    const food = activities.food.reduce(
      (sum, activity) => sum + this.calculateFood(activity),
      0,
    );

    const shopping = activities.shopping.reduce(
      (sum, activity) => sum + this.calculateShopping(activity),
      0,
    );

    const total = transport + energy + food + shopping;

    return {
      transport,
      energy,
      food,
      shopping,
      total,
    };
  }

  static getAverageFootprint(country: string = 'US'): number {
    // Average daily carbon footprint by country (kg CO2 per day)
    const averages = {
      US: 16.1,
      UK: 8.5,
      Germany: 9.6,
      France: 5.2,
      Japan: 8.7,
      China: 7.4,
      India: 1.9,
      Global: 4.8,
    };

    return averages[country as keyof typeof averages] || averages.Global;
  }

  static calculateSavings(
    currentFootprint: number,
    previousFootprint: number,
  ): {
    absolute: number;
    percentage: number;
  } {
    const absolute = previousFootprint - currentFootprint;
    const percentage =
      previousFootprint > 0 ? (absolute / previousFootprint) * 100 : 0;

    return {
      absolute,
      percentage,
    };
  }

  static getRecommendationImpact(
    recommendation: string,
    _userProfile: any,
  ): number {
    // Estimated daily CO2 savings for common recommendations
    const impacts = {
      use_public_transport: 2.5,
      switch_to_led: 0.8,
      reduce_meat: 1.5,
      unplug_electronics: 0.4,
      buy_local: 0.6,
      use_reusable_bags: 0.2,
      shorter_showers: 0.3,
      line_dry_clothes: 0.7,
      bike_to_work: 3.0,
      eat_less_beef: 2.0,
    };

    return impacts[recommendation as keyof typeof impacts] || 0.5;
  }
}

// Simple calculation function for the tracking screen
export function calculateCarbonFootprint(category: string, amount: number): number {
  const defaultFactors = {
    Transport: 0.2, // kg CO2 per km
    Energy: 0.4,    // kg CO2 per kWh
    Food: 2.0,      // kg CO2 per meal
    Shopping: 5.0,  // kg CO2 per item
  };

  const factor = defaultFactors[category as keyof typeof defaultFactors] || 1.0;
  return factor * amount;
}
