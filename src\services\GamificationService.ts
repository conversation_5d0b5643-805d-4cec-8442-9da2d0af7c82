import AsyncStorage from '@react-native-async-storage/async-storage';
import {Achievement} from '../types';
import {CarbonFootprintService} from './CarbonFootprintService';
import {RecommendationsService} from './RecommendationsService';

interface UserStats {
  totalPoints: number;
  currentStreak: number;
  longestStreak: number;
  totalCarbonSaved: number;
  activitiesLogged: number;
  recommendationsCompleted: number;
  lastActivityDate: string | null;
  level: number;
  rank: string;
}

export class GamificationService {
  private static readonly STORAGE_KEYS = {
    USER_STATS: 'ecoai_user_stats',
    ACHIEVEMENTS: 'ecoai_achievements',
    UNLOCKED_ACHIEVEMENTS: 'ecoai_unlocked_achievements',
  };

  private static readonly ACHIEVEMENTS: Achievement[] = [
    {
      id: 'first_activity',
      title: 'Getting Started',
      description: 'Log your first activity',
      icon: 'play-arrow',
      points: 10,
    },
    {
      id: 'week_streak',
      title: 'Week Warrior',
      description: 'Maintain a 7-day streak',
      icon: 'local-fire-department',
      points: 50,
    },
    {
      id: 'month_streak',
      title: 'Monthly Master',
      description: 'Maintain a 30-day streak',
      icon: 'emoji-events',
      points: 200,
    },
    {
      id: 'first_recommendation',
      title: 'Eco Explorer',
      description: 'Complete your first recommendation',
      icon: 'lightbulb',
      points: 20,
    },
    {
      id: 'ten_recommendations',
      title: 'Sustainability Star',
      description: 'Complete 10 recommendations',
      icon: 'star',
      points: 100,
    },
    {
      id: 'carbon_saver_1kg',
      title: 'Carbon Saver',
      description: 'Save 1kg of CO₂',
      icon: 'eco',
      points: 30,
    },
    {
      id: 'carbon_saver_10kg',
      title: 'Eco Champion',
      description: 'Save 10kg of CO₂',
      icon: 'nature',
      points: 150,
    },
    {
      id: 'carbon_saver_50kg',
      title: 'Planet Protector',
      description: 'Save 50kg of CO₂',
      icon: 'public',
      points: 500,
    },
    {
      id: 'transport_hero',
      title: 'Transport Hero',
      description: 'Log 20 sustainable transport activities',
      icon: 'directions-bus',
      points: 75,
    },
    {
      id: 'energy_saver',
      title: 'Energy Saver',
      description: 'Complete 5 energy-saving recommendations',
      icon: 'flash-on',
      points: 60,
    },
  ];

  private static readonly RANKS = [
    {name: 'Eco Newbie', minPoints: 0},
    {name: 'Green Starter', minPoints: 50},
    {name: 'Sustainability Seeker', minPoints: 150},
    {name: 'Eco Enthusiast', minPoints: 300},
    {name: 'Green Guardian', minPoints: 500},
    {name: 'Eco Warrior', minPoints: 800},
    {name: 'Sustainability Master', minPoints: 1200},
    {name: 'Planet Champion', minPoints: 1800},
    {name: 'Eco Legend', minPoints: 2500},
  ];

  static async initializeUserStats(): Promise<void> {
    try {
      const existingStats = await this.getUserStats();
      if (!existingStats) {
        const initialStats: UserStats = {
          totalPoints: 0,
          currentStreak: 0,
          longestStreak: 0,
          totalCarbonSaved: 0,
          activitiesLogged: 0,
          recommendationsCompleted: 0,
          lastActivityDate: null,
          level: 1,
          rank: 'Eco Newbie',
        };
        await this.saveUserStats(initialStats);
      }
    } catch (error) {
      console.error('Error initializing user stats:', error);
    }
  }

  static async getUserStats(): Promise<UserStats | null> {
    try {
      const statsJson = await AsyncStorage.getItem(this.STORAGE_KEYS.USER_STATS);
      return statsJson ? JSON.parse(statsJson) : null;
    } catch (error) {
      console.error('Error getting user stats:', error);
      return null;
    }
  }

  static async saveUserStats(stats: UserStats): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEYS.USER_STATS, JSON.stringify(stats));
    } catch (error) {
      console.error('Error saving user stats:', error);
      throw error;
    }
  }

  static async addPoints(points: number, reason: string): Promise<void> {
    try {
      const stats = await this.getUserStats();
      if (stats) {
        stats.totalPoints += points;
        stats.rank = this.calculateRank(stats.totalPoints);
        stats.level = this.calculateLevel(stats.totalPoints);
        await this.saveUserStats(stats);
        console.log(`Added ${points} points for: ${reason}`);
      }
    } catch (error) {
      console.error('Error adding points:', error);
    }
  }

  static async updateStreak(): Promise<void> {
    try {
      const stats = await this.getUserStats();
      if (!stats) return;

      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      if (stats.lastActivityDate === yesterdayStr) {
        // Continue streak
        stats.currentStreak += 1;
        if (stats.currentStreak > stats.longestStreak) {
          stats.longestStreak = stats.currentStreak;
        }
      } else if (stats.lastActivityDate !== today) {
        // Reset streak if more than one day gap
        stats.currentStreak = 1;
      }

      stats.lastActivityDate = today;
      await this.saveUserStats(stats);
      await this.checkStreakAchievements(stats.currentStreak);
    } catch (error) {
      console.error('Error updating streak:', error);
    }
  }

  static async logActivity(): Promise<void> {
    try {
      const stats = await this.getUserStats();
      if (stats) {
        stats.activitiesLogged += 1;
        await this.saveUserStats(stats);
        await this.updateStreak();
        await this.addPoints(5, 'Activity logged');
        await this.checkActivityAchievements(stats.activitiesLogged);
      }
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  }

  static async completeRecommendation(carbonSaving: number): Promise<void> {
    try {
      const stats = await this.getUserStats();
      if (stats) {
        stats.recommendationsCompleted += 1;
        stats.totalCarbonSaved += carbonSaving;
        await this.saveUserStats(stats);
        await this.addPoints(15, 'Recommendation completed');
        await this.checkRecommendationAchievements(stats.recommendationsCompleted);
        await this.checkCarbonSavingAchievements(stats.totalCarbonSaved);
      }
    } catch (error) {
      console.error('Error completing recommendation:', error);
    }
  }

  private static calculateRank(points: number): string {
    for (let i = this.RANKS.length - 1; i >= 0; i--) {
      if (points >= this.RANKS[i].minPoints) {
        return this.RANKS[i].name;
      }
    }
    return this.RANKS[0].name;
  }

  private static calculateLevel(points: number): number {
    return Math.floor(points / 100) + 1;
  }

  static async getAchievements(): Promise<Achievement[]> {
    return this.ACHIEVEMENTS;
  }

  static async getUnlockedAchievements(): Promise<string[]> {
    try {
      const unlockedJson = await AsyncStorage.getItem(this.STORAGE_KEYS.UNLOCKED_ACHIEVEMENTS);
      return unlockedJson ? JSON.parse(unlockedJson) : [];
    } catch (error) {
      console.error('Error getting unlocked achievements:', error);
      return [];
    }
  }

  static async unlockAchievement(achievementId: string): Promise<boolean> {
    try {
      const unlocked = await this.getUnlockedAchievements();
      if (!unlocked.includes(achievementId)) {
        unlocked.push(achievementId);
        await AsyncStorage.setItem(
          this.STORAGE_KEYS.UNLOCKED_ACHIEVEMENTS,
          JSON.stringify(unlocked),
        );

        const achievement = this.ACHIEVEMENTS.find(a => a.id === achievementId);
        if (achievement) {
          await this.addPoints(achievement.points, `Achievement: ${achievement.title}`);
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error unlocking achievement:', error);
      return false;
    }
  }

  private static async checkActivityAchievements(activitiesLogged: number): Promise<void> {
    if (activitiesLogged === 1) {
      await this.unlockAchievement('first_activity');
    }
  }

  private static async checkStreakAchievements(currentStreak: number): Promise<void> {
    if (currentStreak === 7) {
      await this.unlockAchievement('week_streak');
    } else if (currentStreak === 30) {
      await this.unlockAchievement('month_streak');
    }
  }

  private static async checkRecommendationAchievements(recommendationsCompleted: number): Promise<void> {
    if (recommendationsCompleted === 1) {
      await this.unlockAchievement('first_recommendation');
    } else if (recommendationsCompleted === 10) {
      await this.unlockAchievement('ten_recommendations');
    }
  }

  private static async checkCarbonSavingAchievements(totalCarbonSaved: number): Promise<void> {
    if (totalCarbonSaved >= 1 && totalCarbonSaved < 10) {
      await this.unlockAchievement('carbon_saver_1kg');
    } else if (totalCarbonSaved >= 10 && totalCarbonSaved < 50) {
      await this.unlockAchievement('carbon_saver_10kg');
    } else if (totalCarbonSaved >= 50) {
      await this.unlockAchievement('carbon_saver_50kg');
    }
  }

  static async getLeaderboardData(): Promise<{
    userRank: number;
    userPoints: number;
    totalUsers: number;
  }> {
    try {
      const stats = await this.getUserStats();
      // In a real app, this would fetch from a server
      // For now, we'll simulate some data
      return {
        userRank: Math.floor(Math.random() * 100) + 1,
        userPoints: stats?.totalPoints || 0,
        totalUsers: 1000 + Math.floor(Math.random() * 500),
      };
    } catch (error) {
      console.error('Error getting leaderboard data:', error);
      return {userRank: 0, userPoints: 0, totalUsers: 0};
    }
  }

  static async resetProgress(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.USER_STATS,
        this.STORAGE_KEYS.UNLOCKED_ACHIEVEMENTS,
      ]);
      await this.initializeUserStats();
    } catch (error) {
      console.error('Error resetting progress:', error);
      throw error;
    }
  }
}
