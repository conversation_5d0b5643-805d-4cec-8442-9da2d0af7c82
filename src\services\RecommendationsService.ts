import AsyncStorage from '@react-native-async-storage/async-storage';
import {Recommendation} from '../types';
import {CarbonFootprintService} from './CarbonFootprintService';

export class RecommendationsService {
  private static readonly STORAGE_KEY = 'ecoai_recommendations';

  private static readonly DEFAULT_RECOMMENDATIONS: Omit<Recommendation, 'id' | 'completed'>[] = [
    {
      title: 'Use Public Transport',
      description: 'Take the bus or train instead of driving for your daily commute. This can reduce your transport emissions by up to 45%.',
      category: 'Transport',
      impact: 'High',
      effort: 'Easy',
      carbonSaving: 2.3,
      icon: 'directions-bus',
    },
    {
      title: 'Switch to LED Bulbs',
      description: 'Replace incandescent bulbs with LED lights. They use 75% less energy and last 25 times longer.',
      category: 'Energy',
      impact: 'Medium',
      effort: 'Easy',
      carbonSaving: 0.8,
      icon: 'lightbulb',
    },
    {
      title: 'Reduce Meat Consumption',
      description: 'Try having one meat-free day per week. Plant-based meals have a significantly lower carbon footprint.',
      category: 'Food',
      impact: 'High',
      effort: 'Medium',
      carbonSaving: 1.5,
      icon: 'eco',
    },
    {
      title: 'Unplug Electronics',
      description: 'Unplug devices when not in use. Electronics in standby mode can account for 10% of your electricity bill.',
      category: 'Energy',
      impact: 'Low',
      effort: 'Easy',
      carbonSaving: 0.4,
      icon: 'power',
    },
    {
      title: 'Buy Local Produce',
      description: 'Choose locally grown fruits and vegetables to reduce transportation emissions and support local farmers.',
      category: 'Food',
      impact: 'Medium',
      effort: 'Easy',
      carbonSaving: 0.6,
      icon: 'local-grocery-store',
    },
    {
      title: 'Use Reusable Bags',
      description: 'Bring your own bags when shopping to reduce plastic waste and environmental impact.',
      category: 'Shopping',
      impact: 'Low',
      effort: 'Easy',
      carbonSaving: 0.2,
      icon: 'shopping-bag',
    },
    {
      title: 'Take Shorter Showers',
      description: 'Reduce shower time by 2-3 minutes to save water and energy used for heating.',
      category: 'Energy',
      impact: 'Medium',
      effort: 'Easy',
      carbonSaving: 0.5,
      icon: 'shower',
    },
    {
      title: 'Bike to Work',
      description: 'Replace car trips with cycling for short distances. Great for health and the environment.',
      category: 'Transport',
      impact: 'High',
      effort: 'Medium',
      carbonSaving: 3.0,
      icon: 'directions-bike',
    },
    {
      title: 'Use Cold Water for Laundry',
      description: 'Wash clothes in cold water to reduce energy consumption by up to 90%.',
      category: 'Energy',
      impact: 'Medium',
      effort: 'Easy',
      carbonSaving: 0.7,
      icon: 'local-laundry-service',
    },
    {
      title: 'Reduce Food Waste',
      description: 'Plan meals and store food properly to reduce waste. Food waste contributes significantly to methane emissions.',
      category: 'Food',
      impact: 'High',
      effort: 'Medium',
      carbonSaving: 1.2,
      icon: 'restaurant',
    },
  ];

  static async initializeRecommendations(): Promise<void> {
    try {
      const existingRecommendations = await this.getRecommendations();
      if (existingRecommendations.length === 0) {
        const recommendations: Recommendation[] = this.DEFAULT_RECOMMENDATIONS.map(
          (rec, index) => ({
            ...rec,
            id: `rec_${index + 1}`,
            completed: false,
          }),
        );
        await this.saveRecommendations(recommendations);
      }
    } catch (error) {
      console.error('Error initializing recommendations:', error);
    }
  }

  static async getRecommendations(): Promise<Recommendation[]> {
    try {
      const recommendationsJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (recommendationsJson) {
        return JSON.parse(recommendationsJson);
      }
      return [];
    } catch (error) {
      console.error('Error getting recommendations:', error);
      return [];
    }
  }

  static async saveRecommendations(recommendations: Recommendation[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(recommendations));
    } catch (error) {
      console.error('Error saving recommendations:', error);
      throw error;
    }
  }

  static async toggleRecommendation(recommendationId: string): Promise<void> {
    try {
      const recommendations = await this.getRecommendations();
      const updatedRecommendations = recommendations.map(rec =>
        rec.id === recommendationId ? {...rec, completed: !rec.completed} : rec,
      );
      await this.saveRecommendations(updatedRecommendations);
    } catch (error) {
      console.error('Error toggling recommendation:', error);
      throw error;
    }
  }

  static async getRecommendationsByCategory(category: string): Promise<Recommendation[]> {
    try {
      const allRecommendations = await this.getRecommendations();
      return allRecommendations.filter(rec => rec.category === category);
    } catch (error) {
      console.error('Error getting recommendations by category:', error);
      return [];
    }
  }

  static async getPersonalizedRecommendations(): Promise<Recommendation[]> {
    try {
      const allRecommendations = await this.getRecommendations();
      const today = new Date();
      const activities = await CarbonFootprintService.getActivitiesByDate(today);
      
      // Analyze user's activities to prioritize recommendations
      const categoryUsage = {
        Transport: activities.filter(a => a.category === 'Transport').length,
        Energy: activities.filter(a => a.category === 'Energy').length,
        Food: activities.filter(a => a.category === 'Food').length,
        Shopping: activities.filter(a => a.category === 'Shopping').length,
      };

      // Sort recommendations by relevance (categories with more activities get higher priority)
      const sortedRecommendations = allRecommendations.sort((a, b) => {
        const aUsage = categoryUsage[a.category] || 0;
        const bUsage = categoryUsage[b.category] || 0;
        
        if (aUsage !== bUsage) {
          return bUsage - aUsage; // Higher usage first
        }
        
        // If usage is equal, prioritize by impact and ease
        const impactWeight = {High: 3, Medium: 2, Low: 1};
        const effortWeight = {Easy: 3, Medium: 2, Hard: 1};
        
        const aScore = impactWeight[a.impact] + effortWeight[a.effort];
        const bScore = impactWeight[b.impact] + effortWeight[b.effort];
        
        return bScore - aScore;
      });

      return sortedRecommendations;
    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      return await this.getRecommendations();
    }
  }

  static async getCompletedRecommendations(): Promise<Recommendation[]> {
    try {
      const allRecommendations = await this.getRecommendations();
      return allRecommendations.filter(rec => rec.completed);
    } catch (error) {
      console.error('Error getting completed recommendations:', error);
      return [];
    }
  }

  static async getPendingRecommendations(): Promise<Recommendation[]> {
    try {
      const allRecommendations = await this.getRecommendations();
      return allRecommendations.filter(rec => !rec.completed);
    } catch (error) {
      console.error('Error getting pending recommendations:', error);
      return [];
    }
  }

  static async getRecommendationStats(): Promise<{
    total: number;
    completed: number;
    pending: number;
    totalPotentialSaving: number;
    achievedSaving: number;
  }> {
    try {
      const allRecommendations = await this.getRecommendations();
      const completed = allRecommendations.filter(rec => rec.completed);
      const pending = allRecommendations.filter(rec => !rec.completed);

      const totalPotentialSaving = allRecommendations.reduce(
        (sum, rec) => sum + rec.carbonSaving,
        0,
      );

      const achievedSaving = completed.reduce(
        (sum, rec) => sum + rec.carbonSaving,
        0,
      );

      return {
        total: allRecommendations.length,
        completed: completed.length,
        pending: pending.length,
        totalPotentialSaving,
        achievedSaving,
      };
    } catch (error) {
      console.error('Error getting recommendation stats:', error);
      return {
        total: 0,
        completed: 0,
        pending: 0,
        totalPotentialSaving: 0,
        achievedSaving: 0,
      };
    }
  }

  static async addCustomRecommendation(recommendation: Omit<Recommendation, 'id'>): Promise<void> {
    try {
      const existingRecommendations = await this.getRecommendations();
      const newRecommendation: Recommendation = {
        ...recommendation,
        id: `custom_${Date.now()}`,
      };
      
      const updatedRecommendations = [...existingRecommendations, newRecommendation];
      await this.saveRecommendations(updatedRecommendations);
    } catch (error) {
      console.error('Error adding custom recommendation:', error);
      throw error;
    }
  }

  static async deleteRecommendation(recommendationId: string): Promise<void> {
    try {
      const existingRecommendations = await this.getRecommendations();
      const updatedRecommendations = existingRecommendations.filter(
        rec => rec.id !== recommendationId,
      );
      await this.saveRecommendations(updatedRecommendations);
    } catch (error) {
      console.error('Error deleting recommendation:', error);
      throw error;
    }
  }

  static async resetRecommendations(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      await this.initializeRecommendations();
    } catch (error) {
      console.error('Error resetting recommendations:', error);
      throw error;
    }
  }
}
