import AsyncStorage from '@react-native-async-storage/async-storage';
import {CarbonFootprintService} from './CarbonFootprintService';
import {GamificationService} from './GamificationService';
import {RecommendationsService} from './RecommendationsService';
import {Activity} from '../types';

export class DataSeedingService {
  private static readonly SEEDED_KEY = 'ecoai_data_seeded';

  static async seedSampleData(): Promise<void> {
    try {
      // Check if data has already been seeded
      const hasSeeded = await AsyncStorage.getItem(this.SEEDED_KEY);
      if (hasSeeded === 'true') {
        console.log('Sample data already seeded');
        return;
      }

      console.log('Seeding sample data...');

      // Create sample activities for the past week
      const sampleActivities = this.generateSampleActivities();
      
      // Save activities
      for (const activity of sampleActivities) {
        await CarbonFootprintService.saveActivity(activity);
        await GamificationService.logActivity();
      }

      // Complete some recommendations
      await this.seedRecommendations();

      // Mark as seeded
      await AsyncStorage.setItem(this.SEEDED_KEY, 'true');
      
      console.log('Sample data seeded successfully');
    } catch (error) {
      console.error('Error seeding sample data:', error);
    }
  }

  private static generateSampleActivities(): Activity[] {
    const activities: Activity[] = [];
    const now = new Date();

    // Generate activities for the past 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      // Transport activities
      activities.push({
        id: `transport_${i}_1`,
        userId: 'current_user',
        category: 'Transport',
        description: 'Commute to work by car',
        amount: 15 + Math.random() * 10,
        unit: 'km',
        carbonFootprint: (15 + Math.random() * 10) * 0.2,
        timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
      });

      // Energy activities
      activities.push({
        id: `energy_${i}_1`,
        userId: 'current_user',
        category: 'Energy',
        description: 'Home electricity usage',
        amount: 8 + Math.random() * 4,
        unit: 'kWh',
        carbonFootprint: (8 + Math.random() * 4) * 0.4,
        timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
      });

      // Food activities
      activities.push({
        id: `food_${i}_1`,
        userId: 'current_user',
        category: 'Food',
        description: 'Daily meals',
        amount: 3,
        unit: 'meals',
        carbonFootprint: 3 * 2.0,
        timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
      });

      // Shopping activities (less frequent)
      if (Math.random() > 0.5) {
        activities.push({
          id: `shopping_${i}_1`,
          userId: 'current_user',
          category: 'Shopping',
          description: 'Grocery shopping',
          amount: 1 + Math.random() * 2,
          unit: 'items',
          carbonFootprint: (1 + Math.random() * 2) * 5.0,
          timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
        });
      }
    }

    return activities;
  }

  private static async seedRecommendations(): Promise<void> {
    try {
      // Complete a few recommendations to show progress
      const recommendations = await RecommendationsService.getRecommendations();
      
      // Complete first 3 recommendations
      for (let i = 0; i < Math.min(3, recommendations.length); i++) {
        const recommendation = recommendations[i];
        await RecommendationsService.toggleRecommendation(recommendation.id);
        await GamificationService.completeRecommendation(recommendation.carbonSaving);
      }
    } catch (error) {
      console.error('Error seeding recommendations:', error);
    }
  }

  static async clearSeedData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.SEEDED_KEY);
      console.log('Seed data flag cleared');
    } catch (error) {
      console.error('Error clearing seed data:', error);
    }
  }

  static async resetAndReseed(): Promise<void> {
    try {
      // Clear all data
      await Promise.all([
        CarbonFootprintService.clearAllData(),
        GamificationService.resetProgress(),
        RecommendationsService.resetRecommendations(),
        this.clearSeedData(),
      ]);

      // Re-initialize services
      await Promise.all([
        GamificationService.initializeUserStats(),
        RecommendationsService.initializeRecommendations(),
        CarbonFootprintService.initializeStorage(),
      ]);

      // Seed new data
      await this.seedSampleData();
      
      console.log('Data reset and reseeded successfully');
    } catch (error) {
      console.error('Error resetting and reseeding data:', error);
      throw error;
    }
  }
}
