import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {Achievement} from '../types';

interface AchievementCardProps {
  achievement: Achievement;
  isUnlocked: boolean;
}

const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  isUnlocked,
}) => {
  return (
    <View style={[styles.container, !isUnlocked && styles.lockedContainer]}>
      <View style={[styles.iconContainer, !isUnlocked && styles.lockedIcon]}>
        <Icon
          name={achievement.icon}
          size={32}
          color={isUnlocked ? '#4CAF50' : '#ccc'}
        />
      </View>
      <View style={styles.content}>
        <Text style={[styles.title, !isUnlocked && styles.lockedText]}>
          {achievement.title}
        </Text>
        <Text style={[styles.description, !isUnlocked && styles.lockedText]}>
          {achievement.description}
        </Text>
        <View style={styles.pointsContainer}>
          <Icon name="stars" size={16} color={isUnlocked ? '#FF9800' : '#ccc'} />
          <Text style={[styles.points, !isUnlocked && styles.lockedText]}>
            {achievement.points} points
          </Text>
        </View>
      </View>
      {isUnlocked && (
        <View style={styles.unlockedBadge}>
          <Icon name="check-circle" size={20} color="#4CAF50" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  lockedContainer: {
    backgroundColor: '#f8f8f8',
    opacity: 0.7,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E8F5E8',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  lockedIcon: {
    backgroundColor: '#f0f0f0',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  points: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '500',
    marginLeft: 4,
  },
  lockedText: {
    color: '#999',
  },
  unlockedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
});

export default AchievementCard;
