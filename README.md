# EcoAI - Your Personal Sustainable Living Assistant

EcoAI is a React Native mobile application designed to help users track their carbon footprint, receive personalized recommendations for sustainable living, and gamify their journey towards a more eco-friendly lifestyle.

## Features

### 🌱 Core Features
- **Carbon Footprint Tracking**: Monitor daily activities across transport, energy, food, and shopping
- **AI-Powered Recommendations**: Get personalized suggestions to reduce environmental impact
- **Interactive Dashboard**: Visualize your carbon footprint with charts and trends
- **Gamification**: Earn points, badges, and maintain streaks for sustainable actions
- **Progress Tracking**: Monitor your improvement over time

### 📱 Screens
1. **Dashboard**: Overview of carbon footprint, weekly trends, and quick actions
2. **Tracking**: Log daily activities and see their environmental impact
3. **Recommendations**: Browse and complete eco-friendly suggestions
4. **Profile**: Manage user settings, view achievements, and export data

## Technology Stack

- **Frontend**: React Native with TypeScript
- **Navigation**: React Navigation 6
- **Charts**: React Native Chart Kit
- **Icons**: React Native Vector Icons
- **Styling**: React Native Linear Gradient
- **State Management**: React Hooks (useState, useEffect)

## Project Structure

```
src/
├── screens/           # Main application screens
│   ├── DashboardScreen.tsx
│   ├── TrackingScreen.tsx
│   ├── RecommendationsScreen.tsx
│   └── ProfileScreen.tsx
├── types/            # TypeScript type definitions
│   └── index.ts
├── utils/            # Utility functions
│   └── carbonCalculator.ts
└── App.tsx           # Main application component
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd eco-ai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install iOS dependencies** (iOS only)
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Run the application**
   ```bash
   # For Android
   npm run android

   # For iOS
   npm run ios

   # Start Metro bundler
   npm start
   ```

## Development Setup

### Prerequisites
- Node.js (v16 or higher)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Environment Setup
1. Follow the [React Native Environment Setup](https://reactnative.dev/docs/environment-setup)
2. Ensure you have the required SDKs and tools installed

## Features Implementation Status

### ✅ Completed (MVP)
- [x] Basic app structure and navigation
- [x] Dashboard with carbon footprint visualization
- [x] Activity tracking interface
- [x] Recommendations system
- [x] User profile management
- [x] Carbon footprint calculator
- [x] Basic gamification elements

### 🚧 In Progress
- [ ] Backend API integration
- [ ] User authentication
- [ ] Data persistence
- [ ] Push notifications

### 📋 Planned Features
- [ ] Smart device integration
- [ ] Social features and challenges
- [ ] Advanced analytics
- [ ] Marketplace integration
- [ ] Offline mode
- [ ] Multi-language support

## Carbon Calculation

The app uses scientifically-backed emission factors to calculate carbon footprints:

- **Transport**: Based on vehicle type and distance (kg CO₂/km)
- **Energy**: Based on energy source and consumption (kg CO₂/kWh)
- **Food**: Based on food type and quantity (kg CO₂/kg or serving)
- **Shopping**: Based on item type and quantity (kg CO₂/item)

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Code Style

- Use TypeScript for type safety
- Follow React Native best practices
- Use functional components with hooks
- Maintain consistent styling with StyleSheet
- Follow the existing project structure

## Testing

```bash
# Run tests
npm test

# Run linting
npm run lint

# Format code
npm run format
```

## Deployment

### Android
```bash
# Generate release APK
cd android
./gradlew assembleRelease
```

### iOS
1. Open `ios/EcoAI.xcworkspace` in Xcode
2. Select "Product" > "Archive"
3. Follow App Store submission guidelines

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Carbon emission factors based on EPA and IPCC guidelines
- UI/UX inspired by modern sustainability apps
- Icons provided by Material Design Icons

## Support

For support, email <EMAIL> or create an issue in the repository.

---

**EcoAI Team** - Building a sustainable future, one app at a time 🌍
